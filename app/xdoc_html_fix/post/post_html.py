# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/19 11:31
import re

from app.enums.app_key import AppKey
from app.enums.subject import Subject
from app.basic.util import html_util, latex_util


class PostHtml:

    def __init__(self, task_id: str, html_data: str, app_key: str, subject: str, is_wps: bool):
        self.task_id = task_id
        self.html_data = html_data
        self.app_key = app_key
        self.subject = subject
        self.is_wps = is_wps

    def main(self):
        self.html_data = PostCommon(self.html_data, self.is_wps, self.subject).main()
        if self.app_key in (AppKey.JXW_API.value, AppKey.HEXIN.value):
            self.html_data = PostJXWHtml(self.task_id, self.html_data).main()

        # 去掉 html 的 \n
        self.html_data = self.html_data.replace('\n', '')
        return self.html_data


class PostCommon:

    def __init__(self, html_data: str, is_wps: bool, subject: str):
        self.html_data = html_data
        self.is_wps = is_wps
        self.subject = subject

    def main(self):
        if self.is_wps:
            self.post_wps_process()

        # 按照行处理
        html_list = html_util.split_html_v2(self.html_data)
        for index, html_str in enumerate(html_list):
            html_str = self.remove_duplicate_spans(html_str)
            html_list[index] = html_str
        self.html_data = html_util.join_html(html_list, is_del_line=False)

        self.clean_bracket()

        # 最后，去掉 </p> 后的换行，注意，是最后！
        self.html_data = self.html_data.replace('</p>\n', '</p>')
        return self.html_data

    def post_wps_process(self):
        """
        后处理 ai-edit 的 wps 流程的数据
        """
        def is_material_question():
            return 'data-label="material_start_separator"' in self.html_data

        # 基于规定 1.有材料，可能出现多个一级题号  2.没有材料，不会有多个一级题号
        if not is_material_question():
            html_list = html_util.split_html_v2(self.html_data)
            # 除第一个一级题号，其他的一级题号换为二级
            has_level_1 = False
            for index, html_str in enumerate(html_list):
                if 'data-label="quest_num"' in html_str and 'data-level="1"' in html_str:
                    if not has_level_1:
                        has_level_1 = True
                        continue
                    else:
                        html_str = html_str.replace(' data-label="quest_num"', '')
                        html_str = html_str.replace(' data-level="1"', '')
                        html_list[index] = html_str
            self.html_data = html_util.join_html(html_list, is_del_line=False)

        # wps 流程产出的 html，去掉答案和解析语义化标签
        self.html_data = self.html_data.replace(' data-label="explanation"', '').replace(' data-label="answer"', '')

    def remove_duplicate_spans(self, html):
        """
        删除 html 内重复的 span 标签，data-check="ai_check" 的
        """
        pattern = re.compile(r'<span data-check="ai_check">(<span data-check="ai_check">.*?</span>)</span>')

        # 递归处理嵌套的重复标签
        while True:
            new_html = pattern.sub(r'\1', html)
            if new_html == html:
                break
            html = new_html
        return html

    def clean_bracket(self):
        """
        清洗括号
        英语所有括号都用英文括号，其他学科除公式外，都刷成中文括号
        """
        if self.subject == Subject.english.name:
            self.html_data = self.html_data.replace('（', '(').replace('）', ')')
        else:
            self.html_data, latex_list = latex_util.extract_latex(self.html_data)
            self.html_data = self.html_data.replace('(', '（').replace(')', '）')
            self.html_data = latex_util.restore_latex_v1(self.html_data, latex_list)


class PostJXWHtml:

    def __init__(self, task_id: str, html_data: str):
        self.task_id = task_id
        self.html_data = html_data

    def main(self):
        # 不要三级题号
        self.html_data = re.sub(
            r'<span data-label="quest_num" data-level="3">((?<!</span>)[\S\s]*?)</span>',
            lambda x: x.group(1), self.html_data)

        # 九学王答案都不会在下划线上
        # 制作苯酚降解菌的分离培养基时，培养基中应以苯酚为<span data-label="blank" data-length="4">       </span>，除基本营养成分外
        def repl(m):
            text = m.group(1)
            if re.search(r'^\s*$', text) or 'data-label="blank"' in text:
                return m.group()
            return '<u>' + text + '</u>'
        self.html_data = re.sub(
            r'<span[^>]*?data-label="blank" data-length="\d+"[^>]*?>((?<!</span>)[\S\s]*?)</span>', repl, self.html_data)
        return self.html_data
