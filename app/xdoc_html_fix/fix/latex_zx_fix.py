import asyncio
import collections
import json
import time
import traceback
import re
import ast
import os
from typing import List, Optional

from app.basic.util import latex_util, html_util, utils, time_util
from app.basic.api import deepseek_official
from app.basic.api.doubao import <PERSON><PERSON><PERSON>, DBModel
from app.enums.prompt import GetPrompt
from app.basic.baseerror import VerifyError
from app.basic.log import logger
from app.enums.subject import Subject
from constants import ENV, BASE_DIR
from app.basic.util.db_util import mongo_cli
from app.basic.api.robot import RobotApi


"""
正斜体判断思路：
同时请求 doubao1.5 和 deepseek V3，将输出结果做对比，把不一样的内容收集，最后再用 deepseek R1 跑一遍
"""


class LatexZXFix:

    def __init__(self, html_data: str, subject: str, task_id: str):
        self.html_data = html_data
        self.subject = subject
        self.task_id = task_id

        # 收集失败的 input 最后统一用 deepseek R1 跑
        self.failed_input = []
        # 收集全流程 latex，latex 为 key，value 有个 index list 记录此 latex 出现在哪几个 index 中
        self.latex_index_dict = collections.defaultdict(dict)
        self.latex_count = 0
        self.stat = {'cost_token': 0, 'cache_ratio': 0}

    async def main(self):
        start_time = time.time()
        logger.info(f"{self.task_id} LatexZXFix start.")

        # 限制，仅跑化学学科
        if self.subject not in (Subject.math.name, Subject.physics.name, Subject.chemistry.name, Subject.biology.name):
            logger.info(f"{self.task_id} LatexZXFix {self.subject} 暂未上线")
            return self.html_data, self.stat

        await self.batch_run()

        # 替换回 latex
        # 建立一个空的 latex 列表，长度与文档中 latex 数量一致，然后只把变更的公式写入，这样就能确定出来改了哪些然后回填回 html
        check_html_data = ''
        modified_latex_list: List[Optional[dict]] = [None] * self.latex_count
        for latex_str_pre_post, item in self.latex_index_dict.items():
            index_list: List[int] = item['index_list']
            new_latex_str = item['new_latex_str']
            latex_str = item['latex_str']
            model = item['model']
            # 把没有新 latex 和新旧 latex 一样的情况筛选掉
            if not new_latex_str or latex_str == new_latex_str:
                continue
            origin_latex_html = f'<p><b>刷前：</b>{latex_str_pre_post}</p>\n'
            latex_html = f'<p><b>{model}刷后：</b>{new_latex_str}</p>\n'
            check_html_data += f'{origin_latex_html}{latex_html}'
            for _i in index_list:
                modified_latex_list[_i] = {
                    'latex': new_latex_str,
                    'model': model
                }

        i = 0
        def repl(m):
            nonlocal i
            text = m.group()
            latex_item = modified_latex_list[i]
            if not latex_item:
                i += 1
                return text
            i += 1
            _new_latex_str = latex_item['latex']
            _model = latex_item['model']
            result = html_util.add_check_span(_new_latex_str)
            # 加入 model 信息
            result = result.replace('<span', f'<span data-model="{_model}"')
            return result
        logger.info(f"{self.task_id} LatexZXFix len latex_list:{len(modified_latex_list)}  html latex_count={self.html_data.count('$$') / 2}")
        self.html_data = re.sub(r'\$\$.*?\$\$', repl, self.html_data)

        # 测试流程，保存 check html
        if ENV == 'dev':
            _dir = os.path.join(BASE_DIR, f'scripts/test_zx/temp/check_html/{self.subject}')
            if not os.path.exists(_dir):
                os.makedirs(_dir)
            with open(os.path.join(_dir, f'{self.task_id}.html'), 'w', encoding='utf-8') as f:
                f.write(check_html_data)

        end_time = time.time()
        self.stat.update({'cost_time': int(end_time - start_time)})
        logger.info(f"{self.task_id} LatexZXFix success.")
        return self.html_data, self.stat

    async def batch_run(self):
        self.get_by_html()
        # self.latex_index_dict 会把一样的 latex（包括上下文） 合并，所以 latex_list 是表示去重后的
        latex_list = list(self.latex_index_dict.keys())
        logger.info(f"{self.task_id} LatexZXFix remove duplicate latex count={len(latex_list)}")

        # 使用缓存处理
        latex_list = self.cache_filter(latex_list)

        # print('*' * 100)
        # print(prompt)
        # print('*' * 100)

        # 异步执行，最大并发 50
        latex_chunk_list = utils.chunk_v2(utils.chunk_v2(latex_list, 10), 50)
        for item_chunk_list in latex_chunk_list:
            tasks = [self.model_cross_validate(item) for item in item_chunk_list]
            await asyncio.gather(*tasks)

        logger.info(f"{self.task_id} LatexZXFix 需要使用 R1 的 latex 总数 {len(self.failed_input)}")
        latex_chunk_list = utils.chunk_v2(utils.chunk_v2(self.failed_input, 10), 50)
        for item_chunk_list in latex_chunk_list:
            tasks = [self.model_final_request(item) for item in item_chunk_list]
            await asyncio.gather(*tasks)

    def get_by_html(self):
        """
        获取 html 中的 latex，包括 latex 的上下文
        """
        pre_latex_count = self.html_data.count('$$') / 2
        logger.info(f"{self.task_id} LatexZXFix get_by_html, html latex_count={pre_latex_count}")
        html_list = html_util.split_html_v2(self.html_data)

        pre_length = 6
        post_length = 6
        index = 0
        for data in html_list:
            data = html_util.del_html_tag(data)
            data = html_util.del_html_entities(data)

            def repl(m):
                nonlocal data
                nonlocal index
                latex_text = m.group()
                regs = m.regs[0]
                start_index = regs[0]
                end_index = regs[1]
                # latex 穿透
                pre_data = data[:start_index]
                pre_data = self._clean_pre_post_data(pre_data, 'pre')
                post_data = data[end_index:]
                post_data = self._clean_pre_post_data(post_data, 'post')
                valid_pre_data = pre_data[-pre_length:]
                valid_post_data = post_data[:post_length]
                result = valid_pre_data + latex_text + valid_post_data

                # result 唯一，分组存储，标识这个 result 存储在哪几个 latex 上面
                if self.latex_index_dict.get(result):
                    self.latex_index_dict[result]['index_list'].append(index)
                else:
                    self.latex_index_dict[result] = {
                        'index_list': [index],
                        'latex_str': latex_text,
                        'new_latex_str': '',
                        'model': '',
                    }
                index += 1
            re.sub(r'\$\$.*?\$\$', repl, data)
        self.latex_count = index
        logger.info(f"{self.task_id} LatexZXFix get_by_html, self.latex_count={self.latex_count}")

        # 判断前后 latex 数量不一致，则直接报错
        if pre_latex_count != self.latex_count:
            RobotApi.message(26, "正斜体前后数量不一致", f'task_id={self.task_id}')

    def cache_filter(self, latex_list):
        """
        根据缓存筛选 latex
        此处的逻辑强关联其他两处逻辑：
        1.flush-server 项目中的 cache_xdoc_project_latex task（用来定期缓存新标注完的 latex）
        2.ai-util 项目，scripts 中的批量 cache latex
        """
        def helper(s):
            # 预处理然后转换成 mf5
            pattern = r'\\mathrm\{(.*?)\}'
            while re.search(pattern, s):
                s = re.sub(pattern, lambda match: match.group(1), s)
            md5 = utils.get_str_md5(s)
            return md5

        new_latex_list = []
        cached_count = 0
        for batch, start_index in utils.chunk(latex_list, 50):
            temp, md5_list = {}, []
            for latex_str in batch:
                re_result = re.search(r'(.*?)\$\$(.*?)\$\$(.*)', latex_str)
                pre_text, latex, post_text = re_result.group(1), re_result.group(2), re_result.group(3)
                md5 = helper(latex)
                md5_list.append(md5)
                temp[latex_str] = {'pre_text': pre_text, 'post_text': post_text, 'md5': md5}
            cache_cursor = mongo_cli['ai-util'][f'{self.subject}_latex_cache'].find({'md5': {'$in': list(set(md5_list))}})
            batch_cache_dict = {item['md5']: item for item in cache_cursor}
            for latex_str in batch:
                md5 = temp[latex_str]['md5']
                # 上下文取两个字，使用 contains 去查找
                pre_text = temp[latex_str]['pre_text'][-2:]
                post_text = temp[latex_str]['post_text'][:2]
                cache_dict = batch_cache_dict.get(md5)
                new_latex = ''
                if cache_dict:
                    # 如果根据上下文找不到，则找出现频率最高的 latex
                    latex_count_dict = collections.defaultdict(int)
                    for item in cache_dict['list']:
                        db_latex, db_pre_text, db_pre_text = item['latex'], item['pre'], item['post']
                        latex_count_dict[db_latex] += 1
                        if (pre_text in db_pre_text) or (post_text in db_pre_text):
                            new_latex = db_latex
                            break
                    else:
                        max_count = max(latex_count_dict.values())
                        for db_latex, count in latex_count_dict.items():
                            if count != max_count:
                                continue
                            new_latex = db_latex

                # 命中缓存的做保存，为命中的重新存储走大模型
                if new_latex:
                    cached_count += 1
                    self.latex_index_dict[latex_str]['new_latex_str'] = f'$${new_latex}$$'
                    self.latex_index_dict[latex_str]['model'] = 'cache'
                else:
                    new_latex_list.append(latex_str)

        latex_count = len(latex_list)
        self.stat['cache_ratio'] = utils.round(cached_count / latex_count, 3)
        logger.info(f"{self.task_id} cached latex count={cached_count}")
        return new_latex_list

    async def model_cross_validate(self, latex_list):
        """ 交叉验证 """
        latex_input = []
        for batch_index, b in enumerate(latex_list):
            latex_input.append({
                'id': batch_index + 1,
                'input': b
            })
        latex_input = json.dumps(latex_input, ensure_ascii=False)
        prompt = GetPrompt.fix_latex_zx(latex_input, len(latex_list), self.subject)
        model_res = ''

        try:
            # 整体来说，豆包喜欢自己新加一些无影响的定义，比如 $$-5$$ 会输出 $$\\text{-5}$$，deepseek V3 和 R1 都不会
            db_res_list, ds_v3_res_list = await asyncio.gather(
                self.request_doubao(prompt, len(latex_list)),
                self.request_deepseek_v3(prompt, len(latex_list))
            )
            for i, item in enumerate(db_res_list):
                # 检查错误的 latex 直接放弃，两个模型有一个错误就算错误
                is_correct_db = item['is_correct']
                is_correct_ds = ds_v3_res_list[i]['is_correct']
                if not is_correct_db or not is_correct_ds:
                    self.failed_input.append(latex_list[i])
                    continue

                origin_latex = latex_list[i]
                # 对比两边返回把不一样的输出添加到 failed_input
                doubao_latex = item['fixed']
                deepseek_latex = ds_v3_res_list[i]['fixed']
                # 输出有一定概率带出上下文，只取 latex 对比
                doubao_latex = re.search(r'\$\$.*?\$\$', doubao_latex).group()
                deepseek_latex = re.search(r'\$\$.*?\$\$', deepseek_latex).group()
                # 因为重点是在处理正斜体，所以这里交叉验证只专注 \mathrm 数量
                if doubao_latex.count('mathrm') != deepseek_latex.count('mathrm'):
                    self.failed_input.append(latex_list[i])
                else:
                    # 小模型返回一样，不需要交叉验证，直接返回
                    self.latex_index_dict[origin_latex]['new_latex_str'] = deepseek_latex
                    self.latex_index_dict[origin_latex]['model'] = 'deepseek-V3'
        except Exception as e:
            # 如果请求模型失败，或者是模型输出结果校验失败，则全部的数据都记为失败
            self.failed_input += latex_list
            error_info = str(traceback.format_exc())
            logger.info(f"{self.task_id} doubao & V3 处理过程报错 *** model_res={model_res} *** error_info={error_info}")

    async def model_final_request(self, latex_list):
        """ 交叉验证失败的，使用 R1 最终执行 """
        latex_input = []
        for batch_index, b in enumerate(latex_list):
            latex_input.append({
                'id': batch_index + 1,
                'input': b
            })
        latex_input = json.dumps(latex_input, ensure_ascii=False)
        prompt = GetPrompt.fix_latex_zx(latex_input, len(latex_list), self.subject)
        model_res = ''

        try:
            model_res_list = await self.request_deepseek_r1(prompt, len(latex_list))
            for i, item in enumerate(model_res_list):
                origin_latex = latex_list[i]
                fixed_latex = item['fixed']
                fixed_latex = re.search(r'\$\$.*?\$\$', fixed_latex).group()
                self.latex_index_dict[origin_latex]['new_latex_str'] = fixed_latex
                self.latex_index_dict[origin_latex]['model'] = 'deepseek-R1'
        except Exception as e:
            error_info = str(traceback.format_exc())
            logger.info(f"{self.task_id} R1 处理过程报错 *** model_res={model_res} *** error_info={error_info}")

    async def request_doubao(self, prompt, input_count):
        # 请求 doubao
        model_res, cost_token = await Doubao.async_chat(prompt, DBModel.V15_PRO_32.value, 0.1)
        self.stat['cost_token'] += cost_token
        model_res = model_res.strip()
        model_res_list = self.check_model_res(model_res, input_count)
        return model_res_list

    async def request_deepseek_v3(self, prompt, input_count):
        if time_util.is_time_to_run_ds_official():
            # deepseek 官网
            model_res, cost_token = await deepseek_official.async_v3(prompt, temperature=0.1)
        else:
            model_res, cost_token = await Doubao.async_chat(prompt, DBModel.DS_V3.value, 0.1)
        self.stat['cost_token'] += cost_token
        model_res = model_res.strip()
        model_res_list = self.check_model_res(model_res, input_count)
        return model_res_list

    async def request_deepseek_r1(self, prompt, input_count):
        if time_util.is_time_to_run_ds_official():
            # deepseek 官网
            model_res, cost_token = await deepseek_official.async_r1(prompt, temperature=0.1)
        else:
            model_res, cost_token = await Doubao.async_chat(prompt, DBModel.DS_R1.value, 0.1)
        self.stat['cost_token'] += cost_token
        model_res = model_res.strip()
        model_res_list = self.check_model_res(model_res, input_count)
        return model_res_list

    def check_model_res(self, model_res, input_count):
        model_res = (model_res.replace('```JSON', '').replace('```json', '').replace('JSON', '').
                     replace('json', '').replace('```', ''))
        # 是否可结构化检查
        try:
            model_res_list = ast.literal_eval(model_res)
        except Exception as e:
            raise VerifyError(f"{self.task_id} error 算法结果结构化失败, model_res={model_res}")

        # 结构正确性检查
        for index, r in enumerate(model_res_list):
            _input, fixed = r.get('input', ''), r.get('fixed', '')
            if r.get('id') and _input.count('$$') == 2 and fixed.count('$$') == 2:
                is_correct = True
            else:
                is_correct = False
            if is_correct:
                _input_latex = re.search(r'\$\$.*?\$\$', _input).group()
                fixed_latex = re.search(r'\$\$.*?\$\$', fixed).group()
                # 中文字符数对比
                _input_char_count = utils.chinese_chars_count(_input_latex)
                fixed_char_count = utils.chinese_chars_count(fixed_latex)
                if _input_char_count != fixed_char_count:
                    is_correct = False
                # 数字字符对比
                _input_char_count = utils.digit_chars_count(_input_latex)
                fixed_char_count = utils.digit_chars_count(fixed_latex)
                if _input_char_count != fixed_char_count:
                    is_correct = False
            model_res_list[index]['is_correct'] = is_correct

        # 输出数量检查
        if input_count != len(model_res_list):
            raise VerifyError(f"{self.task_id} error 输出数量不对！")
        return model_res_list

    def _clean_pre_post_data(self, data, tag):
        data = re.sub(r'\$\$.*?\$\$', '', data)
        t = '.,;:?!，。、；：？！“”‘’（）【】《》■\s'
        if tag == 'pre':
            data = re.sub(rf'.*[{t}]', '', data)
        if tag == 'post':
            data = re.sub(rf'[{t}].*', '', data)
        data = data.strip()
        return data
