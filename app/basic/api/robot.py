from app.basic.baseerror import InternalError
from app.basic.request import Request
from app.basic.log import logger
from constants import ROBOT_HOST


class RobotApi(Request):
    base_url = ROBOT_HOST

    USER_ID_LINK = {
        '王世华': 'ou_19927c899f78394681186e4197e05c1a',
        '王一萌': 'gc2a9251',
        '邓慧兰': 'bcda31dd',
        '甘露': 'b42e1g4a',
        '张庆龄': 'e363g9b7',
    }

    @classmethod
    def get_data(cls, url, json: dict):
        if not json['code']:
            return json.get('data')
        logger.error(f'''{url}：{json['code']} {json['message']}''')
        raise InternalError(json['message'], label='Hexin-Robot 系统')

    @classmethod
    def message(cls, receive_id, title, text, user_id: str = '', username: str = ''):
        url = '/api/message/send/default'
        data = {
            "type": 'info',
            "receive_id": receive_id,
            "server": {
                "name": "ai-util",
            },
            "content": {
                'title': title,
                'text': text
            },
            "user_id": user_id,
            "username": username
        }
        cls.post(url=url, json=data)


if __name__ == '__main__':
    RobotApi.message(
        receive_id=26,
        title='latex 正斜体过程 check',
        text=""
    )
