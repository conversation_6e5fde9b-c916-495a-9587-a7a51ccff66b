import json
import os

from app.enums.task import TaskType
from scripts import COOKIE
from worker.xdoc_html_fix_worker import XdocHtmlFixWorker
import requests
from app.basic.storagehandler import storage
from worker.title_level_worker import <PERSON>LevelWorker
from app.basic.log import logger
from app.xdoc_html_fix.multi_round_fix.main import MultiRoundFix
from app.basic.util import html_util


class TestXdocHtmlFix:

    def __init__(self, task_id_list: list, is_ai_edit: bool, tag: str, start_from_parsed: bool = False):
        self.task_id_list = task_id_list
        self.is_ai_edit = is_ai_edit
        self.tag = tag
        self.start_from_parsed = start_from_parsed  # 新增：控制是否从解析后开始

    async def main(self):
        total_stat = []
        for tid in self.task_id_list:
            res = requests.get(
                url=f'http://xdoc.open.hexinedu.com/api/admin/taskV2/getOneById',
                params={
                    'taskId': tid
                },
                cookies={'UBUS': 'M4S-CRGoa1rxT7-5YQav_oFK4py54c1xzvLOmHfds1NZSTS2DI7dncKUI7I3ZPuL'}
            ).json()
            task_info = res['data']
            html_url = task_info['html']
            subject = task_info['meta']['subject']
            app_key = task_info['appKey']

            if self.is_ai_edit:
                machine_html_url = html_url.replace('.html', '.backup.html')
            else:
                machine_html_url = html_url.replace('.html', '.machine.html')

            # 测试 HTML 片段
            # machine_html_url = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/static/ai-util/test_html_fix.html'

            logger.info(f"task_id={tid}")
            logger.info(f"subject={subject}")
            logger.info(f"app_key={app_key}")
            logger.info(f"html_url={html_url}")
            logger.info(f"machine_html_url={machine_html_url}")
            logger.info(f"start_from_parsed={self.start_from_parsed}")
            print()

            if self.start_from_parsed:
                # 从解析后开始：直接进入答案修复阶段
                await self.debug_answer_fix(tid, machine_html_url, subject, app_key)
            else:
                # 从头开始：完整流程
                await self.full_process(tid, machine_html_url, subject, app_key, html_url, total_stat)

    async def debug_answer_fix(self, task_id, machine_html_url, subject, app_key):
        """
        直接进入答案修复阶段的调试方法
        """
        try:
            # 使用本地HTML文件作为输入
            local_html_path = f"/Users/<USER>/work/ai-util/app/xdoc_html_fix/multi_round_fix/{task_id}.machine.html"

            # 检查本地文件是否存在
            if os.path.exists(local_html_path):
                logger.info(f"{task_id} 使用本地HTML文件: {local_html_path}")
                with open(local_html_path, 'r', encoding='utf-8') as f:
                    html_data = f.read()
                print(f"从本地文件加载HTML: {local_html_path}")
            else:
                logger.warning(f"{task_id} 本地文件不存在，使用远程URL: {machine_html_url}")
                html_data = requests.get(machine_html_url).content.decode('utf-8')
                print(f"从远程URL加载HTML: {machine_html_url}")

            # 初始化 HTML 格式
            html_list = html_util.split_html_v2(html_data, is_add_line=False)
            html_data = '\n'.join(html_list)

            logger.info(f"{task_id} 开始答案修复调试...")

            # 直接调用 MultiRoundFix 进行答案修复
            html_data, stat = await MultiRoundFix(
                html_data, subject, task_id, machine_html_url, self.tag,
                TaskType.XDOC_HTML_FIX_PRIORITY.value, self.is_ai_edit, False
            ).main()

            logger.info(f"{task_id} 答案修复完成")
            print(f"答案修复统计: {stat}")
            print('答案修复调试完成.')

        except Exception as e:
            logger.error(f"{task_id} 答案修复调试失败: {e}")
            print(f'{task_id} 答案修复调试失败，error={e}')

    async def full_process(self, task_id, machine_html_url, subject, app_key, html_url, total_stat):
        """
        完整的处理流程
        """
        try:
            # 前置跑标题
            callback_info = TitleLevelWorker(
                task_id=task_id,
                html_url=machine_html_url,
                subject=subject,
                bucket_name='xdoc-stable',
                upload_path='open/fc7539b21810cd4f0f0fb620/task/19123911.html',
                is_ai_edit=True,
                is_test=True,
            ).main()
            machine_html_url = 'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/fc7539b21810cd4f0f0fb620/task/19123911.html'

            callback_info = await XdocHtmlFixWorker(
                task_id=task_id,
                html_url=machine_html_url,
                subject=subject,
                app_key=app_key,
                bucket_name='xdoc-stable',
                upload_path='open/fc7539b21810cd4f0f0fb620/task/19809373.html',
                word_type='',
                is_ai_edit=self.is_ai_edit,
                task_type=TaskType.XDOC_HTML_FIX_PRIORITY.value,
                is_test=True,
                tag=self.tag,
            ).main()
            status = callback_info['status']  # 0 成功  1 失败

            if status == 0:
                # 成功
                stat = callback_info['stat']
                total_stat.append(stat)
                print('success.')
            else:
                # 失败
                print(f'{task_id} 处理失败，error={callback_info["info"]}')

        except Exception as e:
            logger.error(f"{task_id} 完整流程处理失败: {e}")
            print(f'{task_id} 完整流程处理失败，error={e}')


if __name__ == '__main__':
    import asyncio

    # 配置参数
    task_id_list = ['20777629']  # 任务ID列表
    is_ai_edit = False           # 是否AI编辑
    tag = 'jxw'                  # 标签

    # 控制参数：True=从解析后开始(答案修复调试), False=从头开始(完整流程)
    start_from_parsed = False  # 修改这里来控制启动模式

    print(f"启动模式: {'从解析后开始(答案修复调试)' if start_from_parsed else '从头开始(完整流程)'}")
    print(f"任务ID: {task_id_list}")
    print(f"标签: {tag}")
    print("-" * 50)

    loop = asyncio.get_event_loop()
    loop.run_until_complete(
        TestXdocHtmlFix(
            task_id_list=task_id_list,
            is_ai_edit=is_ai_edit,
            tag=tag,
            start_from_parsed=start_from_parsed
        ).main()
    )
