# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/3/8 12:36
import asyncio

from app.basic.api.mq import MQ
from app.basic.log import logger
from app.enums.task import TaskType
from app.basic.baseerror import ParamsError
from worker.xdoc_html_fix_worker import XdocHtmlFixWorker
from worker.title_level_worker import TitleLevelWorker
from worker.pdf_comment_worker import PdfCommentWorker
from worker.json_duplicate_check_worker import <PERSON><PERSON><PERSON><PERSON><PERSON>teCheckWorker
from app.basic.util import time_util, db_util


class RunTask:

    def __init__(self, task_type: str):
        self.task_type = task_type

        if self.task_type == TaskType.XDOC_HTML_FIX.value:
            if time_util.is_time_to_run_ds_official():
                self.task_info = MQ.get_task(self.task_type)
            elif db_util.is_run_xdoc_html_fix():
                self.task_info = MQ.get_task(self.task_type)
            else:
                if time_util.is_output_log():
                    logger.info('不到任务执行时间并且数据库设置的不允许执行！')
                self.task_info = None
        else:
            self.task_info = MQ.get_task(self.task_type)

    async def main(self):
        if not self.task_info:
            # logger.info(f'{self.task_type} 未获取到任务，稍后将重试！')
            return

        task_id = str(self.task_info['task_id'])
        logger.info(f"task_type={self.task_info}")
        if self.task_type in (TaskType.XDOC_HTML_FIX_PRIORITY.value, TaskType.XDOC_HTML_FIX.value):
            html_url = self.task_info['html_url']
            subject = self.task_info['subject']
            app_key = self.task_info['app_key']
            bucket_name = self.task_info['bucket_name']
            upload_path = self.task_info['upload_path']
            word_type = self.task_info.get('word_type', '')
            is_ai_edit = self.task_info.get('is_ai_edit', False)

            if self.task_type == TaskType.XDOC_HTML_FIX_PRIORITY.value:
                await XdocHtmlFixWorker(
                    task_id, html_url, subject, app_key, bucket_name, upload_path, word_type, is_ai_edit, self.task_type).main()
            elif self.task_type == TaskType.XDOC_HTML_FIX.value:
                await XdocHtmlFixWorker(
                    task_id, html_url, subject, app_key, bucket_name, upload_path, word_type, is_ai_edit, self.task_type).main()

        elif self.task_type == TaskType.TITLE_LEVEL.value:
            html_url = self.task_info['html_url']
            subject = self.task_info['subject']
            bucket_name = self.task_info['bucket_name']
            upload_path = self.task_info['upload_path']
            is_ai_edit = self.task_info.get('is_ai_edit', False)
            TitleLevelWorker(task_id, html_url, subject, bucket_name, upload_path, is_ai_edit).main()

        elif self.task_type == TaskType.PDF_COMMENT.value:
            ticket_id = self.task_info['ticket_id']
            comment_dict = self.task_info['comment_dict']
            PdfCommentWorker(task_id, ticket_id, comment_dict).main()

        elif self.task_type == TaskType.JSON_DUPLICATE_CHECK.value:
            json_url = self.task_info['json_url']
            bucket_name = self.task_info['bucket_name']
            upload_path = self.task_info['upload_path']
            JsonDuplicateCheckWorker(task_id, json_url, bucket_name, upload_path).main()

        else:
            raise ParamsError(f"暂不支持 task_type {self.task_type}")


if __name__ == '__main__':
    asyncio.run(RunTask(TaskType.TITLE_LEVEL.value).main())
